import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider } from '@/context/Theme';
import { Theme } from '@/utils/Enums';

// Import updated components for testing
import { RouteChange } from '@/components/RouteChangeModal/RouteChange';
import BackButton from '@/components/BackButton/BackButton';
import BasicTextarea from '@/components/BasicTextarea/BasicTextarea';
import Calendar from '@/components/Calendar/Calendar';
import MkdCalendar from '@/components/MkdCalendar/MkdCalendar';
import Container from '@/components/Container/Container';
import ExportButton from '@/components/ExportButton/ExportButton';
import HeaderLogo from '@/components/HeaderLogo/HeaderLogo';
import Loader from '@/components/Loader/Loader';
import LoadingIndicator from '@/components/LoadingIndicator/LoadingIndicator';
import ProgressBar from '@/components/ProgressBar/ProgressBar';
import QrCodeGenerator from '@/components/QrCodeGenerator/QrCodeGenerator';
import QrCodeReader from '@/components/QrCodeReader/QrCodeReader';
import CircularImagePreview from '@/components/CircularImagePreview/CircularImagePreview';
import Title from '@/components/Title/Title';

// Mock necessary hooks and contexts
jest.mock('@/hooks/useContexts', () => ({
  useContexts: () => ({
    globalState: { isOpen: true, path: '/' },
    globalDispatch: jest.fn(),
    authDispatch: jest.fn(),
  }),
}));

jest.mock('@/hooks/useProfile', () => ({
  useProfile: () => ({
    profile: { name: 'Test User' },
  }),
}));

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => jest.fn(),
  Link: ({ children, ...props }: any) => <a {...props}>{children}</a>,
}));

// Test wrapper with theme provider
const TestWrapper: React.FC<{ theme: Theme; children: React.ReactNode }> = ({ 
  theme, 
  children 
}) => {
  return (
    <ThemeProvider>
      <div className={theme === Theme.DARK ? 'dark' : ''}>
        {children}
      </div>
    </ThemeProvider>
  );
};

describe('Theme Integration Tests', () => {
  const testThemes = [Theme.LIGHT, Theme.DARK];

  describe('Core UI Components', () => {
    testThemes.forEach((theme) => {
      describe(`${theme} theme`, () => {
        test('RouteChange component renders with theme', () => {
          const options = [
            { name: 'Home', route: '/' },
            { name: 'About', route: '/about' },
          ];

          render(
            <TestWrapper theme={theme}>
              <RouteChange onClose={jest.fn()} options={options} />
            </TestWrapper>
          );

          expect(screen.getByText('Home')).toBeInTheDocument();
          expect(screen.getByText('About')).toBeInTheDocument();
        });

        test('BackButton component renders with theme', () => {
          render(
            <TestWrapper theme={theme}>
              <BackButton text="Go Back" />
            </TestWrapper>
          );

          expect(screen.getByText('Go Back')).toBeInTheDocument();
        });

        test('BasicTextarea component renders with theme', () => {
          render(
            <TestWrapper theme={theme}>
              <BasicTextarea placeholder="Enter text..." />
            </TestWrapper>
          );

          const textarea = screen.getByPlaceholderText('Enter text...');
          expect(textarea).toBeInTheDocument();
        });

        test('Container component renders with theme', () => {
          render(
            <TestWrapper theme={theme}>
              <Container>
                <div>Test Content</div>
              </Container>
            </TestWrapper>
          );

          expect(screen.getByText('Test Content')).toBeInTheDocument();
        });

        test('ExportButton component renders with theme', () => {
          render(
            <TestWrapper theme={theme}>
              <ExportButton onClick={jest.fn()} />
            </TestWrapper>
          );

          expect(screen.getByText('Export')).toBeInTheDocument();
        });

        test('Loader component renders with theme', () => {
          render(
            <TestWrapper theme={theme}>
              <Loader />
            </TestWrapper>
          );

          // Loader should render without errors
          expect(document.querySelector('.flex')).toBeInTheDocument();
        });

        test('LoadingIndicator component renders with theme', () => {
          render(
            <TestWrapper theme={theme}>
              <LoadingIndicator />
            </TestWrapper>
          );

          // LoadingIndicator should render motion divs
          expect(document.querySelector('.flex')).toBeInTheDocument();
        });

        test('ProgressBar component renders with theme', () => {
          render(
            <TestWrapper theme={theme}>
              <ProgressBar percentage={75} />
            </TestWrapper>
          );

          expect(screen.getByText('75%')).toBeInTheDocument();
        });

        test('Title component renders with theme', () => {
          render(
            <TestWrapper theme={theme}>
              <Title>Test Title</Title>
            </TestWrapper>
          );

          expect(screen.getByText('Test Title')).toBeInTheDocument();
        });
      });
    });
  });

  describe('Specialized Components', () => {
    testThemes.forEach((theme) => {
      describe(`${theme} theme`, () => {
        test('QrCodeGenerator component renders with theme', () => {
          render(
            <TestWrapper theme={theme}>
              <QrCodeGenerator setResult={jest.fn()} />
            </TestWrapper>
          );

          expect(screen.getByText('Generate QR code')).toBeInTheDocument();
        });

        test('QrCodeReader component renders with theme', () => {
          render(
            <TestWrapper theme={theme}>
              <QrCodeReader setResult={jest.fn()} />
            </TestWrapper>
          );

          expect(screen.getByText('Scan QR Code')).toBeInTheDocument();
        });

        test('CircularImagePreview component renders with theme', () => {
          render(
            <TestWrapper theme={theme}>
              <CircularImagePreview image="https://example.com/image.jpg" />
            </TestWrapper>
          );

          const image = screen.getByRole('img');
          expect(image).toBeInTheDocument();
        });
      });
    });
  });

  describe('Theme Switching', () => {
    test('Components respond to theme changes', () => {
      const { rerender } = render(
        <TestWrapper theme={Theme.LIGHT}>
          <Container>
            <Title>Theme Test</Title>
          </Container>
        </TestWrapper>
      );

      // Initial render with light theme
      expect(screen.getByText('Theme Test')).toBeInTheDocument();

      // Re-render with dark theme
      rerender(
        <TestWrapper theme={Theme.DARK}>
          <Container>
            <Title>Theme Test</Title>
          </Container>
        </TestWrapper>
      );

      // Component should still be rendered
      expect(screen.getByText('Theme Test')).toBeInTheDocument();
    });
  });

  describe('Interactive Elements', () => {
    test('Theme-aware interactive elements work correctly', () => {
      const mockOnClick = jest.fn();
      
      render(
        <TestWrapper theme={Theme.LIGHT}>
          <ExportButton onClick={mockOnClick} />
        </TestWrapper>
      );

      const button = screen.getByText('Export');
      fireEvent.click(button);
      
      expect(mockOnClick).toHaveBeenCalled();
    });

    test('Focus states work with theme integration', () => {
      render(
        <TestWrapper theme={Theme.LIGHT}>
          <BasicTextarea placeholder="Focus test" />
        </TestWrapper>
      );

      const textarea = screen.getByPlaceholderText('Focus test');
      fireEvent.focus(textarea);
      fireEvent.blur(textarea);
      
      // Should not throw any errors
      expect(textarea).toBeInTheDocument();
    });
  });
});
