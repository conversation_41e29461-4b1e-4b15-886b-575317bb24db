// list out all folders in the directory
const fs = require("fs");
const path = require("path");

// define the path to the directory
const folderPath = path.join(__dirname);

// list out all folders in the directory
const items = fs.readdirSync(folderPath);

// filter only folders and get their full paths
const folders = items
  .filter((item) => {
    const itemPath = path.join(folderPath, item);
    return fs.statSync(itemPath).isDirectory();
  })
  .map((folder) => {
    return path.join(folderPath, folder);
  });

console.log("Full folder paths:");
folders.forEach((folderPath) => {
  console.log(folderPath);
});

//  src/components/AdminHeader
//  src/components/AdminWrapper
//  src/components/CircularImagePreview
//  src/components/CodeEditor
//  src/components/Collapser
//  src/components/CollapsibleMenu
//  src/components/Container
//  src/components/CreateNewRoomModal
//  src/components/CustomSelect
//  src/components/DashboardUI
//  src/components/DateRange
//  src/components/Deployment
//  src/components/DisplayDomainUrl
//  src/components/DropdownOptions
//  src/components/DynamicContentType
//  src/components/Editor
//  src/components/EditWireframeTabs
//  src/components/ErrorBoundary
//  src/components/ExportButton
//  src/components/ExternalUI
//  src/components/HeaderLogo
//  src/components/HorizontalNavbar
//  src/components/ImagePreviewModal
//  src/components/InteractiveButton
//  src/components/InteractiveMap
//  src/components/LandingPage
//  src/components/LazyLoad
//  src/components/Loader
//  src/components/LoadingIndicator
//  src/components/MkdButton
//  src/components/MkdCalendar
//  src/components/MkdControlledInput
//  src/components/MkdDebounceInput
//  src/components/MkdFileTable
//  src/components/MKDForm
//  src/components/MkdInfiniteScroll
//  src/components/MkdInput
//  src/components/MkdInputV2
//  src/components/MkdJsonQuiz
//  src/components/MkdListTable
//  src/components/MkdLoader
//  src/components/MkdPasswordInput
//  src/components/MkdPopover
//  src/components/MkdSimpleTable
//  src/components/MkdTabContainer
//  src/components/MkdWizardContainer
//  src/components/Modal
//  src/components/ModalSidebar
//  src/components/MultipleAnswer
//  src/components/MultiSelect
//  src/components/Notifications
//  src/components/OfflineAwareForm
//  src/components/OfflineExample
//  src/components/OfflineIndicator
//  src/components/OfflineNotifications
//  src/components/OfflineStatusBar
//  src/components/PaginationBar
//  src/components/ProfileImageUpload
//  src/components/ProgressBar
//  src/components/PublicHeader
//  src/components/PublicWrapper
//  src/components/QrCodeGenerator
//  src/components/QrCodeReader
//  src/components/RatingStar
//  src/components/RouteChangeModal
//  src/components/SearchableDropdown
//  src/components/SessionExpiredModal
//  src/components/SimpleViewWrapper
//  src/components/Skeleton
//  src/components/SnackBar
//  src/components/SyncDashboard
//  src/components/SyncStatusIndicator
//  src/components/ThemeStyles
//  src/components/ThemeToggle
//  src/components/Title
//  src/components/TitleDetail
//  src/components/Toast
//  src/components/TopBarSticky
//  src/components/TopHeader
//  src/components/UploadConfig
//  src/components/UserProfile
//  src/components/video
//  src/components/ViewModelItem
//  src/components/ViewWrapper
