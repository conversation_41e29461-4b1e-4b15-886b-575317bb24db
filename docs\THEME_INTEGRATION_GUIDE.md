# Theme Integration Guide

## Overview

This guide documents the comprehensive theme integration implemented across all components in the MKD application. The theme system supports both light and dark modes with consistent styling and smooth transitions.

## Theme System Architecture

### Core Components
- **ThemeProvider**: Context provider that manages theme state
- **ThemeStyles**: Component that injects CSS variables based on current theme
- **useTheme**: Hook for accessing theme state and actions
- **THEME_COLORS**: Constants defining color palettes for each theme

### Theme Structure
```typescript
interface ThemeColors {
  PRIMARY: string;
  PRIMARY_HOVER: string;
  PRIMARY_ACTIVE: string;
  PRIMARY_DISABLED: string;
  BACKGROUND: string;
  BACKGROUND_SECONDARY: string;
  BACKGROUND_HOVER: string;
  TEXT: string;
  TEXT_HOVER: string;
  TEXT_ON_PRIMARY: string;
  TEXT_SECONDARY: string;
  BORDER: string;
  INPUT_BACKGROUND: string;
  SHADOW: string;
  // ... and many more
}
```

## Updated Components

### ✅ Core UI Components (Completed)
- **RouteChangeModal** - Full theme integration with hover/focus states
- **BackButton** - Theme-aware colors and interactive states
- **BasicTextarea** - Theme-aware input styling with focus states
- **Loader** - Theme-aware background colors
- **LoadingIndicator** - Theme-aware dot colors using PRIMARY
- **ExportButton** - Theme-aware button styling with interactions
- **Container** - Theme-aware background and shadow
- **Title** - Theme-aware text and background colors

### ✅ Layout Components (Completed)
- **AdminWrapper** - Already had theme integration
- **PublicWrapper** - Already had theme integration
- **HeaderLogo** - Updated with theme-aware colors and interactions
- **HorizontalNavbar** - Partial update (navigation styling)

### ✅ Data Display Components (Completed)
- **Calendar** - Full theme integration including FullCalendar styling
- **MkdCalendar** - Theme-aware calendar with custom CSS injection
- **ProgressBar** - Theme-aware progress visualization
- **MkdFileTable** - Already had theme integration
- **MkdListTable** - Already had theme integration (RowDropdown)

### ✅ Specialized Components (Completed)
- **QrCodeGenerator** - Theme-aware QR code generation and UI
- **QrCodeReader** - Theme-aware file upload and result display
- **CircularImagePreview** - Theme-aware image container with hover effects

### ✅ Offline & Sync Components (Completed)
- **OfflineIndicator** - Theme-aware status indicators
- **OfflineNotifications** - Theme-aware notification styling

### 🔄 Components with Partial Theme Support
These components already work with the theme system through Tailwind CSS variables:
- **InteractiveButton** - Uses CSS variables (border-primary, bg-primary)
- **MkdButton** - Uses CSS variables (border-primary, bg-primary)
- **Modal** - Uses CSS variables (bg-background, text-text, border-border)
- **PaginationBar** - Uses CSS variables (bg-background, text-text)
- **MkdInputV2** - Uses theme CSS variables
- **MkdSimpleTable** - Uses CSS variables for theming

## Implementation Patterns

### Basic Theme Integration Pattern
```typescript
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";

const Component = () => {
  const { state } = useTheme();
  const mode = state?.theme;

  const themeStyles = {
    color: THEME_COLORS[mode].TEXT,
    backgroundColor: THEME_COLORS[mode].BACKGROUND,
    borderColor: THEME_COLORS[mode].BORDER,
  };

  return (
    <div style={themeStyles} className="transition-colors duration-200">
      {/* Component content */}
    </div>
  );
};
```

### Interactive Elements Pattern
```typescript
const InteractiveComponent = () => {
  const { state } = useTheme();
  const mode = state?.theme;

  return (
    <button
      className="transition-all duration-200 hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2"
      style={{
        backgroundColor: THEME_COLORS[mode].PRIMARY,
        color: THEME_COLORS[mode].TEXT_ON_PRIMARY,
      }}
      onFocus={(e) => {
        e.currentTarget.style.boxShadow = `0 0 0 2px ${THEME_COLORS[mode].PRIMARY}40`;
      }}
      onBlur={(e) => {
        e.currentTarget.style.boxShadow = '';
      }}
    >
      Button Text
    </button>
  );
};
```

### CSS Variable Integration Pattern
```typescript
// For components that use Tailwind CSS variables
const CSSVariableComponent = () => {
  return (
    <div className="bg-background text-text border-border transition-colors duration-200">
      {/* This automatically works with theme switching */}
    </div>
  );
};
```

## Theme Features

### Automatic Theme Detection
- Detects system preference on first load
- Remembers user's theme choice in localStorage
- Applies theme class to document root

### Smooth Transitions
- All components include `transition-colors duration-200` for smooth theme switching
- Interactive states have appropriate transition effects

### Accessibility
- Proper focus states with theme-aware colors
- Sufficient color contrast in both themes
- Keyboard navigation support maintained

### Responsive Design
- Theme integration works across all screen sizes
- Mobile-friendly touch targets maintained

## Testing

### Automated Tests
- Comprehensive test suite in `src/test/theme-integration.test.tsx`
- Tests both light and dark themes
- Verifies component rendering and interactions
- Ensures theme switching works correctly

### Manual Testing Checklist
- [ ] Theme toggle works in header
- [ ] All components render correctly in light theme
- [ ] All components render correctly in dark theme
- [ ] Smooth transitions when switching themes
- [ ] Interactive states work properly
- [ ] Focus states are visible and theme-appropriate
- [ ] No console errors during theme switching

## Browser Support
- Modern browsers with CSS custom properties support
- Graceful fallback for older browsers
- Tested in Chrome, Firefox, Safari, and Edge

## Performance Considerations
- CSS variables provide efficient theme switching
- Minimal JavaScript overhead
- Optimized re-renders using React context
- Smooth 200ms transitions for better UX

## Future Enhancements
- Additional theme variants (high contrast, etc.)
- Per-component theme customization
- Theme-aware animations
- Advanced color palette generation

## Troubleshooting

### Common Issues
1. **Component not updating on theme change**: Ensure component uses `useTheme` hook
2. **Styling conflicts**: Check for hardcoded colors overriding theme styles
3. **Performance issues**: Verify efficient use of CSS variables vs inline styles

### Debug Tips
- Use browser dev tools to inspect CSS custom properties
- Check React DevTools for theme context state
- Verify theme class is applied to document root
